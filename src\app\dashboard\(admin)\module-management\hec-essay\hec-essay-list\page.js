'use client';
import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import NewTablePage from '@/components/form/NewTablePage';
import useDataFetch from '@/hooks/useDataFetch';
import Image from 'next/image';

const EssayList = () => {
  const router = useRouter();
  const [filteredAwards, setFilteredAwards] = useState([]);
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(10);
  const [totalPages, setTotalPages] = useState(1);

  // Use the provided useDataFetch hook
  const { data, isLoading, refetch } = useDataFetch({
    queryKey: ['/admin-essay/list', page, limit],
    endPoint: '/admin-essay/list',
    params: { page, limit },
    enabled: true
  });

  // Process data whenever it changes
  useEffect(() => {
    if (data && data.items) {
      // Process essay data with submission and review information
      const processedEssays = data.items.map((essay, index) => {
        // Get the latest submission from submissionHistory
        const latestSubmission = essay.submissionHistory &&
          essay.submissionHistory.length > 0 ?
          essay.submissionHistory[0] : null;
        
        // Extract submission and review information
        return {
          ...essay,
          '#': index + 1,
          submittedBy: latestSubmission ?
            (latestSubmission.updatedBy || 'Tutor User') : 'Tutor User',
          // Make sure reviewedBy is properly assigned when status is 'reviewed'
          reviewedBy: essay.status === 'reviewed' ?
            (essay.updatedBy || 'Tutor User') : '',
          reviewStatus: essay.status === 'reviewed' ?
            'Reviewed' : 'Not Reviewed Yet',
          points: latestSubmission && latestSubmission.submissionMark ?
            latestSubmission.submissionMark.points : 'N/A',
          feedback: latestSubmission && latestSubmission.submissionMark ?
            latestSubmission.submissionMark.submissionFeedback : 'No feedback provided',
          remarks: latestSubmission && latestSubmission.submissionMark ?
            latestSubmission.submissionMark.taskRemarks : 'No remarks provided',
          content: latestSubmission ? latestSubmission.content : 'No content available',
          submissionDate: latestSubmission ? 
            new Date(latestSubmission.submissionDate).toLocaleDateString() : 'Unknown'
        };
      });
      
      setTotalPages(Math.ceil(data.totalCount / limit));
      setFilteredAwards(processedEssays);
    }
  }, [data, limit]);

  // Pagination handler - to be passed to NewTablePage
  const changePage = (newPage) => {
    setPage(newPage);
  };

  // Handler for rows per page change
  const handleRowsPerPageChange = (newLimit) => {
    setLimit(newLimit);
    setPage(1); // Reset to first page when changing limit
  };

  // Navigate directly to essay details page with correct route
  const viewEssayDetails = (essay) => {
    router.push(`/dashboard/module-management/hec-essay/essay-details/${essay._id}`);
  };

  // User Avatar component with standard user icon
  const UserAvatar = ({ name }) => {
    return (
      <div className="flex items-center justify-center h-8 w-8 rounded-full bg-amber-100 text-amber-800">
        <svg 
          className="h-5 w-5" 
          xmlns="http://www.w3.org/2000/svg" 
          viewBox="0 0 24 24" 
          fill="currentColor"
        >
          <path 
            fillRule="evenodd" 
            d="M7.5 6a4.5 4.5 0 119 0 4.5 4.5 0 01-9 0zM3.751 20.105a8.25 8.25 0 0116.498 0 .75.75 0 01-.437.695A18.683 18.683 0 0112 22.5c-2.786 0-5.433-.608-7.812-1.7a.75.75 0 01-.437-.695z" 
            clipRule="evenodd" 
          />
        </svg>
      </div>
    );
  };

  // Define table columns for NewTablePage with custom cell renderer
  const columns = [
   
    { 
      field: 'submittedBy', 
      label: 'SUBMITTED BY', 
      sortable: true,
      cellRenderer: (value, row) => (
        <div className="flex items-center space-x-2">
          <UserAvatar name={value} />
          <span className="font-medium text-gray-900">{value}</span>
        </div>
      )
    },
    { 
      field: 'reviewStatus', 
      label: 'REVIEW STATUS', 
      sortable: true,
      cellRenderer: (value, row) => (
        <div className="flex items-center">
          {value === 'Reviewed' ? (
            <div className="flex items-center bg-green-50 text-green-700 px-4 py-2 rounded">
              <span className="text-sm font-medium">Reviewed</span>
              <svg className="ml-2 h-4 w-4 text-green-700" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
              </svg>
            </div>
          ) : (
            <div className="flex items-center bg-amber-50 text-amber-700 px-4 py-2 rounded">
              <span className="text-sm font-medium">Not Reviewed Yet</span>
              {/* Hourglass icon */}
              <svg className="ml-2 h-4 w-4 text-amber-700" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
              </svg>
            </div>
          )}
        </div>
      )
    },
    { 
      field: 'reviewedBy', 
      label: 'REVIEWED BY', 
      sortable: true,
      cellRenderer: (value, row) => {
        // Show reviewer details when essay has been reviewed
        return row.reviewStatus === 'Reviewed' ? (
          <div className="flex items-center space-x-2">
            <UserAvatar name={value} />
            <span className="font-medium text-gray-900">{value || row.updatedBy}</span>
          </div>
        ) : null;
      }
    },
    {
      field: 'action',
      label: 'ACTION',
      sortable: false,
      cellRenderer: (value, row) => (
        <button
          onClick={() => viewEssayDetails(row)}
          className="inline-flex items-center justify-center p-2 rounded-full bg-blue-50 text-blue-600 hover:bg-blue-100 transition-colors"
          aria-label="View essay details"
        >
          <svg className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
            <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
            <path fillRule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clipRule="evenodd" />
          </svg>
        </button>
      )
    }
  ];

  return (
    <div className="bg-white p-6 rounded-lg">
      <h1 className="text-xl font-semibold mb-6">HEC Essay List</h1>
      
      {/* NewTablePage component with passed handlers */}
      <NewTablePage
        title=""
        createButton="Create Essay"
        createBtnLink="/essays/create"
        columns={columns}
        data={filteredAwards}
        currentPage={page}
        totalPages={totalPages}
        changePage={changePage}
        loading={isLoading}
        totalItems={data?.totalCount || 0}
        rowsPerPage={limit}
        setRowsPerPage={handleRowsPerPageChange}
        showCheckboxes={false}
        showSearch={false}
        searchPlaceholder="Search by Name"
        showNameFilter={false}
        showSortFilter={false}
        showCreateButton={false}
        paginationLabel="Rows per page:"
      />
    </div>
  );
};

export default EssayList;