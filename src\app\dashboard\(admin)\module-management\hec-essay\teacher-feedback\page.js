'use client';
import { useRef, useEffect } from 'react';
import Image from 'next/image';

/**
 * TeacherFeedbackModal Component
 * 
 * A modal component for displaying teacher feedback for a student essay.
 * Styled to match the wooden sign design from Figma.
 * 
 * @param {Object} props
 * @param {Object} props.essay - The essay object containing feedback details
 * @param {Function} props.onClose - Function to call when closing the modal
 * @returns {JSX.Element|null} The TeacherFeedbackModal component or null if not shown
 */
const TeacherFeedbackModal = ({ essay, onClose }) => {
  const modalRef = useRef(null);

  useEffect(() => {
    // Handle escape key press
    const handleEscapeKey = (e) => {
      if (e.key === 'Escape') {
        onClose();
      }
    };

    // Handle click outside
    const handleClickOutside = (e) => {
      if (modalRef.current && !modalRef.current.contains(e.target)) {
        onClose();
      }
    };

    // Add event listeners
    document.addEventListener('keydown', handleEscapeKey);
    document.addEventListener('mousedown', handleClickOutside);
    
    // Prevent body scrolling when modal is open
    document.body.style.overflow = 'hidden';

    // Cleanup
    return () => {
      document.removeEventListener('keydown', handleEscapeKey);
      document.removeEventListener('mousedown', handleClickOutside);
      // Restore body scrolling when modal is closed
      document.body.style.overflow = 'auto';
    };
  }, [onClose]);

  if (!essay) return null;

  // Format the feedback points as an array if they're not already
  const feedbackPoints = Array.isArray(essay.feedback) 
    ? essay.feedback 
    : essay.feedback?.split('\n').filter(point => point.trim().length > 0) || [];

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
      <div 
        ref={modalRef}
        className="bg-white rounded-lg shadow-xl w-full max-w-2xl max-h-[90vh] overflow-hidden relative"
      >
        {/* Close button - Round orange/amber with X */}
        <div className="absolute top-4 right-4 z-10">
          <button
            type="button"
            onClick={onClose}
            className="focus:outline-none"
            aria-label="Close"
          >
            <Image 
              src="/assets/images/all-img/cross-bg.png" 
              alt="Close" 
              width={40} 
              height={40} 
              className="w-10 h-10" 
            />
          </button>
        </div>
        
        {/* Wooden Sign Header */}
        <div className="bg-[#FFF9FB] pt-6 pb-8 px-6 border-b border-amber-200">
          <div className="flex justify-center mb-2">
            <div className="relative w-full max-w-md">
              {/* Wooden sign background with text overlay */}
              <Image
                src="/assets/images/all-img/teacheressay-bg.png"
                alt="Mission Essay"
                width={600}
                height={200}
                className="w-full h-auto"
                priority
              />

              
              {/* Text overlay on wooden sign */}
              <div className="absolute inset-0 flex flex-col items-center justify-center">
                {/* Crown icon */}
                <div className="flex justify-center mb-2">
                  <svg className="h-8 w-8 text-amber-800" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M12 2L9.19 8.63L2 9.24L7.46 13.97L5.82 21L12 17.27L18.18 21L16.54 13.97L22 9.24L14.81 8.63L12 2Z" />
                  </svg>
                </div>
                
              </div>
            </div>
          </div>
        </div>
        
        {/* Feedback Content */}
        <div className="px-8 py-6">
          <h3 className="text-2xl font-semibold mb-4 text-gray-800">Feedback</h3>
          <ol className="list-decimal pl-6 space-y-4">
            {feedbackPoints.map((point, index) => (
              <li key={index} className="text-gray-700">{point}</li>
            ))}
            {feedbackPoints.length === 0 && (
              <p className="text-gray-500 italic">No feedback provided yet.</p>
            )}
          </ol>
        </div>
        
        {/* Divider */}
        <div className="border-t border-gray-200 mx-8"></div>
        
        {/* Footer with action buttons */}
        <div className="flex justify-end p-4 bg-gray-50">
          <button
            type="button"
            className="bg-amber-600 text-white px-4 py-2 rounded-md hover:bg-amber-700 focus:outline-none focus:ring-2 focus:ring-amber-500"
            onClick={onClose}
          >
            Close
          </button>
        </div>
      </div>
    </div>
  );
};

export default TeacherFeedbackModal;