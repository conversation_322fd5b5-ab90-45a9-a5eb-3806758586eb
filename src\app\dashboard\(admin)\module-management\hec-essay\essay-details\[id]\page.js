'use client';
import { useState } from 'react';
import { useRouter } from 'next/navigation';

const EssayDetails = ({ params }) => {
  const router = useRouter();
  const [showFeedbackModal, setShowFeedbackModal] = useState(false);
  
  // Mock data based on the Figma design
  const essay = {
    id: params?.id || '1',
    title: 'A picnic journey',
    submittedBy: 'MD <PERSON>',
    submissionDate: '29th May, 2025',
    content: 'I went to school by bus. Today was a fun day. I met my Friends and went to the park. We play games and had a picnic',
    originalDate: '5th March,25',
    tutorEditDate: '5th March,25'
  };

  // Handle back button click
  const handleBackClick = () => {
    router.back();
  };

  return (
    <div className="bg-white min-h-screen w-full">
      <div className="max-w-7xl mx-auto pt-16 ml-14">
        {/* Header with view essay title */}
        <h1 className=" font-normal mb-4 text-xl ">View Essay</h1>
        
        {/* User info and date section */}
        <div className="flex justify-between items-center mb-6">
          <div className="flex flex-col items-start space-y-1">
  <span className="text-sm font-normal">Submitted by:</span>
  <div className="flex items-center space-x-2 ">
    <div className="w-6 h-6 rounded-full bg-amber-100 flex items-center justify-center text-amber-800 overflow-hidden mt-2">
      {/* User Avatar Circle */}
      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
        <path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd" />
      </svg>
    </div>
    <span className="text-xs text-gray-700 mt-2">{essay.submittedBy}</span>
  </div>
</div>

          
          <div className="flex flex-col items-center text-xs text-gray-700 space-y-1">
  <span className="text-sm font-normal mr-10">Date:</span>
  <div className="flex items-center space-x-2 ml-7">
    <div className="text-yellow-300 text-lg bg-yellow-50 rounded-full p-1 flex items-center justify-center">
      {/* Calendar icon */}
      <svg className="h-4 w-4" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
        <path fillRule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clipRule="evenodd" />
      </svg>
    </div>
    <span>{essay.submissionDate}</span>
  </div>
</div>

        </div>
        
        {/* Main content grid - adjusted for proper layout based on Figma */}
        <div className="flex bg-[#FDE7E9] rounded-md mt-9">
          {/* Left Panel - Frame with decorative elements */}
          <div className="w-1/2 p-3 relative">
            <div className="bg-white border-amber-800 rounded-lg h-full flex items-center justify-center overflow-hidden">
              {/* Decorative frame content */}
              <div className="relative w-full h-full">
                <div className="border-8 border-amber-800 rounded-lg p-4 h-full">
                  <div className="border border-dashed border-amber-400 rounded-lg p-4 h-full flex flex-col justify-between">
                    {/* Title at the top */}
                    <div className="text-center mb-2">
                      <h3 className="text-lg font-semibold">{essay.title}</h3>
                    </div>
                    
                    {/* Essay content in the middle */}
                    <div className="flex-grow text-sm text-gray-700 overflow-auto p-2">
                      <p>I went to school by bus. Today was a fun day. I met my Friends and went to the park. We play games and had a picnic</p>
                    </div>
                    
                   
                  </div>
                </div>
                
                {/* Yellow decorative bows */}
                <div className="absolute top-0 left-1/4 -translate-y-1/2">
                  <div className="w-6 h-6 bg-yellow-300 rounded-full"></div>
                </div>
                <div className="absolute top-0 right-1/4 -translate-y-1/2">
                  <div className="w-6 h-6 bg-yellow-300 rounded-full"></div>
                </div>
                <div className="absolute bottom-0 left-1/4 translate-y-1/2">
                  <div className="w-6 h-6 bg-yellow-300 rounded-full"></div>
                </div>
                <div className="absolute bottom-0 right-1/4 translate-y-1/2">
                  <div className="w-6 h-6 bg-yellow-300 rounded-full"></div>
                </div>
                <div className="absolute left-0 top-1/4 -translate-x-1/2">
                  <div className="w-6 h-6 bg-yellow-300 rounded-full"></div>
                </div>
                <div className="absolute left-0 bottom-1/4 -translate-x-1/2">
                  <div className="w-6 h-6 bg-yellow-300 rounded-full"></div>
                </div>
                <div className="absolute right-0 top-1/4 translate-x-1/2">
                  <div className="w-6 h-6 bg-yellow-300 rounded-full"></div>
                </div>
                <div className="absolute right-0 bottom-1/4 translate-x-1/2">
                  <div className="w-6 h-6 bg-yellow-300 rounded-full"></div>
                </div>
              </div>
            </div>
          </div>
          
          {/* Right Panel - Editor sections */}
          <div className="w-1/2 flex flex-col space-y-4 p-3">
            {/* Top right box - Original Essay */}
            <div className="bg-white border border-gray-200 rounded-lg p-4 flex-1">
              <div className="flex justify-between mb-2">
                <span className="font-semibold text-sm">{essay.title}</span>
                <span className="text-xs">Date: {essay.originalDate}</span>
              </div>
              <div className="h-24 overflow-auto bg-white text-xs text-gray-700 p-2 border border-gray-200 rounded">
                <p>I went to school by bus. Today was a fun day. I met my Friends and went to the park. We play games and had a picnic</p>
              </div>
            </div>
            
            {/* Bottom right box - Tutor Editing Zone */}
            <div className="bg-white border border-gray-200 rounded-lg p-4 flex-1">
              <div className="flex justify-between mb-1">
                <span className="font-semibold text-sm">{essay.title}</span>
                <span className="text-xs">Date: {essay.tutorEditDate}</span>
              </div>
              <div className="text-center text-xs text-yellow-700 mb-2">
                Tutor Editing Zone
              </div>
              <div className="h-24 overflow-auto bg-white text-xs text-gray-700 p-2 border border-gray-200 rounded relative">
                <p>I went to school by bus. Today was a fun day. I met my Friends and went to the park. We play games and had a picnic</p>
                
                {/* Trophy/Achievement icon */}
                <div className="absolute bottom-2 right-2 w-8 h-8 bg-amber-500 rounded-full flex items-center justify-center text-white cursor-pointer">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
                  </svg>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        {/* Footer buttons */}
        <div className="mt-6 flex justify-end space-x-4">
          <button
            onClick={() => setShowFeedbackModal(true)}
            className="px-4 py-2 bg-amber-500 text-white rounded-lg hover:bg-amber-600 transition-colors"
          >
            View Feedback
          </button>
          <button
            onClick={handleBackClick}
            className="px-4 py-2 bg-white border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
          >
            Back to List
          </button>
        </div>
      </div>
      
      {/* Feedback Modal */}
      {showFeedbackModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl w-3/4 max-w-3xl max-h-[90vh] overflow-y-auto">
            <div className="p-6 border-b flex justify-between items-center">
              <h3 className="text-xl font-semibold">Teacher Feedback</h3>
              <button 
                onClick={() => setShowFeedbackModal(false)}
                className="p-2 rounded-full hover:bg-gray-200"
              >
                <svg className="h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            <div className="p-6">
              <p>This is placeholder feedback content. Implement the TeacherFeedbackModal component here.</p>
            </div>
            <div className="p-6 border-t flex justify-end">
              <button
                onClick={() => setShowFeedbackModal(false)}
                className="px-4 py-2 bg-amber-500 text-white rounded-lg hover:bg-amber-600 transition-colors"
              >
                Close
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default EssayDetails;