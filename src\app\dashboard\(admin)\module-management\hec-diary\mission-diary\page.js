'use client';
import NewTablePage from '@/components/form/NewTablePage';
import useDataFetch from '@/hooks/useDataFetch';
import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import DeleteModal from '@/components/form/modal/DeleteModal';

const MissionDiary = () => {
  const router = useRouter();
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [currentPage, setCurrentPage] = useState(1);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [selectedMission, setSelectedMission] = useState(null);

  const { data, isLoading, refetch } = useDataFetch({
    queryKey: ['mission-diary', currentPage, rowsPerPage],
    endPoint: '/diary/admin/missions',
    params: { page: currentPage, limit: rowsPerPage },
  });
  const missionDiary = data?.items || [];

  // Pagination logic
  const totalItems = data?.meta?.totalItems || missionDiary.length;
  const paginatedQuestions = missionDiary;

  // Define columns for the table
  const columns = [
    {
      label: 'Mission Title',
      field: 'title',
    },
  ];

  const actions = [
    {
      name: 'view',
      icon: 'material-symbols:visibility',
      className: 'text-blue-600',
      onClick: (row) =>
        router.push(
          `/dashboard/module-management/hec-diary/mission-diary/details/${row?.id}`
        ),
    },
    {
      name: 'edit',
      icon: 'material-symbols:edit-outline',
      className: 'text-gray-600',
      onClick: (row) =>
        router.push(
          `/dashboard/module-management/hec-diary/mission-diary/edit/${row?.id}`
        ),
    },
    {
      name: 'delete',
      icon: 'heroicons-outline:trash',
      className: 'text-red-600',
      onClick: (row) => {
        setSelectedMission(row);
        setShowDeleteModal(true);
      },
    },
  ];

  // Handle page change
  const handleChangePage = (page) => {
    setCurrentPage(page);
  };

  return (
    <div>
      <NewTablePage
        title="Mission Diary"
        createButton="Add Mission"
        createBtnLink="/dashboard/module-management/hec-diary/mission-diary/add"
        columns={columns}
        data={paginatedQuestions}
        actions={actions}
        currentPage={currentPage}
        changePage={handleChangePage}
        totalItems={totalItems}
        rowsPerPage={rowsPerPage}
        setRowsPerPage={setRowsPerPage}
        totalPages={Math.ceil(totalItems / rowsPerPage)}
        showCheckboxes={false}
        showSearch={false}
        showNameFilter={false}
        showSortFilter={false}
        showCreateButton={true}
        isLoading={isLoading}
      />

      {showDeleteModal && (
        <DeleteModal
          isOpen={showDeleteModal}
          onClose={() => setShowDeleteModal(false)}
          data={selectedMission}
          endPoint={`/diary/admin/missions/${selectedMission?.id}`}
          onSuccess={refetch}
        />
      )}
    </div>
  );
};

export default MissionDiary;
