'use client';
import BasicTablePage from '@/components/form/BasicTablePage';
import React, { useState } from 'react';
import { useRouter } from 'next/navigation';

const DiaryList = () => {
  const router = useRouter();
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [currentPage, setCurrentPage] = useState(1);

  // Use mock data if API data is not available
  const diaryEntries = [
    {
      id: '1',
      student: '<PERSON>',
      status: 'Pending',
      reviewedBy: 'Admin',
    },
    {
      id: '2',
      student: 'Smith Row',
      status: 'Reviewed',
      reviewedBy: 'Super Admin',
    },
    {
      id: '3',
      student: '<PERSON>',
      status: 'Reviewed',
      reviewedBy: 'Admin',
    },
    {
      id: '4',
      student: '<PERSON>',
      status: 'Pending',
      reviewedBy: 'Admin',
    },
    {
      id: '5',
      student: '<PERSON>',
      status: 'Reviewed',
      reviewedBy: 'Admin',
    },
  ];

  // Pagination logic
  const totalItems = diaryEntries.length;
  const totalPages = Math.ceil(totalItems / rowsPerPage);

  // If using mock data, paginate locally
  const paginatedEntries = diaryEntries.slice(
    (currentPage - 1) * rowsPerPage,
    currentPage * rowsPerPage
  );

  const tableData = diaryEntries.map((record, index) => {
    return {
      serial: (currentPage - 1) * rowsPerPage + index + 1,
      student: record.student || 'Unknown',
      status: (
        <span
          className={`inline-flex items-center px-3 py-1.5 rounded text-sm font-medium text-gray-700 ${
            record.status === 'Reviewed'
              ? 'bg-[#E0F2E9]'
              : 'bg-yellow-100'
          }`}
        >
          {record.status}
        </span>
      ),
      reviewedBy: record.reviewedBy || 'Unknown',

    };
  });

  // Define columns for the table
  const columns = [
    {
      label: 'SL',
      field: 'serial',
    },
    {
      label: 'USER NAME',
      field: 'student',
    },
    {
      label: 'STATUS',
      field: 'status',
    },
    {
      label: 'REVIEWED BY',
      field: 'reviewedBy',
    },
    {
      label: 'ACTION',
      field: '',
    },
  ];

  // Define actions
  const actions = [
    {
      name: 'view',
      icon: 'material-symbols:visibility',
      className: 'text-blue-600',
      onClick: (index) => {
        const entry = paginatedEntries[index];
        router.push(
          `/dashboard/module-management/hec-diary/diary-list/${entry.id}`
        );
      },
    }
  ];

  // Handle page change
  const handleChangePage = (page) => {
    setCurrentPage(page);
  };

  return (
    <div>
      <BasicTablePage
        title="HEC Diary Entries"
        createButton="Add Diary Entry"
        createBtnLink="/dashboard/admin/module-management/hec-diary/create-diary"
        columns={columns}
        data={tableData}
        actions={actions}
        currentPage={currentPage}
        changePage={handleChangePage}
        totalItems={totalItems}
        rowsPerPage={rowsPerPage}
        setRowsPerPage={setRowsPerPage}
        totalPages={totalPages}
      />
    </div>
  );
};

export default DiaryList;
