'use client';

import { useState, useEffect } from 'react';
import { Icon } from '@iconify/react';
import { useRouter } from 'next/navigation';
import NewTablePage from '@/components/form/NewTablePage';
import useDataFetch from '@/hooks/useDataFetch';

const EssayAwards = () => {
  const router = useRouter();
  const [activeTab, setActiveTab] = useState('monthly');
  const [filteredAwards, setFilteredAwards] = useState([]);
  const [search, setSearch] = useState('');
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(10);
  const [totalPages, setTotalPages] = useState(1);
  const [sortBy, setSortBy] = useState('date');
  const [sortDirection, setSortDirection] = useState('desc');
  const [searchField, setSearchField] = useState('name');

  // Use the provided useDataFetch hook
  const { data, isLoading, refetch } = useDataFetch({
    queryKey: ['awards', page, limit, search],
    endPoint: `/awards${search.length > 2 ? `?name=${search}` : ''}`,
    params: { page, limit },
    enabled: search.length < 1 || search.length > 2
  });

  // Process and filter data whenever it changes
  useEffect(() => {
    if (data && data.items) {
      // Filter to only include essay awards
      const essayAwards = data.items.filter(award => award.module === 'essay');
      setTotalPages(Math.ceil(essayAwards.length / limit));
      filterAwards(essayAwards, activeTab);
    }
  }, [data, activeTab, sortBy, sortDirection]);

  // Filter awards based on tab
  const filterAwards = (awardsData, tab) => {
    const filtered = awardsData.filter(award => {
      if (tab === 'monthly') {
        return award.frequency === 'monthly';
      } else if (tab === 'yearly') {
        return award.frequency === 'yearly';
      }
      return true;
    });
    
    // Sort filtered awards
    const sorted = sortAwards(filtered, sortBy);
    setFilteredAwards(sorted);
  };

  // Sort awards based on selected criteria
  const sortAwards = (awardsToSort, criteria) => {
    return [...awardsToSort].sort((a, b) => {
      if (criteria === 'date') {
        return sortDirection === 'desc' 
          ? new Date(b.startDate || 0) - new Date(a.startDate || 0)
          : new Date(a.startDate || 0) - new Date(b.startDate || 0);
      } else if (criteria === 'name') {
        return sortDirection === 'desc'
          ? b.name.localeCompare(a.name)
          : a.name.localeCompare(b.name);
      } else if (criteria === 'points') {
        return sortDirection === 'desc'
          ? (b.rewardPoints || 0) - (a.rewardPoints || 0)
          : (a.rewardPoints || 0) - (b.rewardPoints || 0);
      }
      return 0;
    });
  };

  // Handle tab change
  const handleTabChange = (tab) => {
    setActiveTab(tab);
    if (data && data.items) {
      const essayAwards = data.items.filter(award => award.module === 'essay');
      filterAwards(essayAwards, tab);
    }
    setPage(1); // Reset to first page when changing tabs
  };

  // Handler for search - to be passed to NewTablePage
  const handleSearch = (value) => {
    setSearch(value);
    
    // Reset to first page when searching
    if (page !== 1) {
      setPage(1);
    }
  };

  // Handler for search field change - to be passed to NewTablePage
  const handleSearchFieldChange = (field) => {
    setSearchField(field);
    
    // Reset to first page when changing search field
    if (page !== 1) {
      setPage(1);
    }
  };

  // Handler for sort change - to be passed to NewTablePage
  const handleSort = (field) => {
    // If clicking on the same field, toggle direction
    if (sortBy === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      // New field, default to ascending
      setSortBy(field);
      setSortDirection('asc');
    }
    
    // Re-sort using the current data
    if (data && data.items) {
      const essayAwards = data.items.filter(award => award.module === 'essay');
      filterAwards(essayAwards, activeTab);
    }
  };

  // Pagination handler - to be passed to NewTablePage
  const changePage = (newPage) => {
    setPage(newPage);
  };

  // Handler for rows per page change
  const handleRowsPerPageChange = (newLimit) => {
    setLimit(newLimit);
    setPage(1); // Reset to first page when changing limit
  };

  // View award details
  const viewAwardDetails = (award) => {
    router.push(`/awards/${award.id}`);
  };

  // Define table columns for NewTablePage
  const columns = [
    { field: 'name', label: 'Award Title', sortable: true },
    { field: 'rewardPoints', label: 'Reward Points', sortable: true }
  ];

  // Define actions for NewTablePage
  const actions = [
    {
      icon: 'material-symbols:visibility',
      className: 'text-blue-500 hover:text-blue-700',
      onClick: (row) => viewAwardDetails(row)
    }
  ];

  // Define name filter options
  const nameFilterOptions = [
    { label: 'Award Title', value: 'name' },
    { label: 'Reward Points', value: 'rewardPoints' }
  ];

  // Format data for table with properly formatted dates
  const formattedData = filteredAwards.map(award => ({
    ...award,
    startDate: award.startDate ? new Date(award.startDate).toLocaleDateString() : '-',
    endDate: award.endDate ? new Date(award.endDate).toLocaleDateString() : '-',
    rewardPoints: award.rewardPoints || 0
  }));

  return (
    <div className="bg-white p-6 rounded-lg shadow">
      <h1 className="text-2xl font-bold mb-6">Award List</h1>
      
      {/* Tabs - Keep original tab UI */}
      <div className="flex mb-6 border-b">
        <button
          className={`py-2 px-4 font-medium hover:bg-[#FEFCE8] transition-colors duration-200 ${
            activeTab === 'monthly' 
              ? 'border-b-2 border-yellow-400 text-black' 
              : 'text-gray-500'
          }`}
          onClick={() => handleTabChange('monthly')}
        >
          Monthly Awards
        </button>
        <button
          className={`py-2 px-4 font-medium hover:bg-[#FEFCE8] transition-colors duration-200 ${
            activeTab === 'yearly' 
              ? 'border-b-2 border-yellow-400 text-black' 
              : 'text-gray-500'
          }`}
          onClick={() => handleTabChange('yearly')}
        >
          Annual Awards
        </button>
      </div>

      {/* NewTablePage component with passed handlers */}
      <NewTablePage
        title=""
        createButton="Create Award"
        createBtnLink="/awards/create"
        showCreateButton={false}
        columns={columns}
        actions={actions}
        data={formattedData}
        currentPage={page}
        totalPages={totalPages}
        changePage={changePage}
        loading={isLoading}
        totalItems={filteredAwards.length}
        rowsPerPage={limit}
        setRowsPerPage={handleRowsPerPageChange}
        // Pass the handlers
        onSearch={handleSearch}
        onNameFilterChange={handleSearchFieldChange}
        onSort={handleSort}
        // Pass the current state
        searchTerm={search}
        searchField={searchField}
        sortField={sortBy}
        sortDirection={sortDirection}
        nameFilterOptions={nameFilterOptions}
      />

      {/* Refresh button
      <div className="mt-4 flex justify-end">
        <button
          onClick={refetch}
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
          <Icon icon="material-symbols:refresh" className="mr-2 h-5 w-5" />
          Refresh
        </button>
      </div> */}
    </div>
  );
};

export default EssayAwards;