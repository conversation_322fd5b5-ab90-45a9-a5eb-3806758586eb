'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import NewTablePage from '@/components/form/NewTablePage';
import useDataFetch from '@/hooks/useDataFetch';
import { Icon } from '@iconify/react';
const TodaysDiary = () => {
  const router = useRouter();

  // State variables
  const [searchTerm, setSearchTerm] = useState('');
  const [searchField, setSearchField] = useState('studentName');
  const [sortField, setSortField] = useState('createdAt');
  const [sortDirection, setSortDirection] = useState('DESC');
  const [currentPage, setCurrentPage] = useState(1);
  const [rowsPerPage, setRowsPerPage] = useState(10);

  // Fetch diary submissions using useDataFetch hook
  const {
    data: diaryData,
    isLoading: loading,
    error
  } = useDataFetch({
    queryKey: ['tutor-diary-pending-reviews', currentPage, rowsPerPage, sortField, sortDirection],
    endPoint: '/tutor/diary/pending-reviews',
    params: {
      page: currentPage,
      limit: rowsPerPage,
      sortBy: sortField,
      sortDirection: sortDirection
    }
  });

  // Log any errors
  if (error) {
    console.error('Error fetching diary data:', error);
  }

  // Extract data from the response
  // Based on the SkinManagement component, useDataFetch already processes the API response
  // and returns the data property directly
  const submissions = diaryData?.items || [];
  const totalItems = diaryData?.totalCount || diaryData?.totalItems || 0;
  const totalPages = diaryData?.totalPages || 1;

  // Handle page change
  const handlePageChange = (page) => {
    setCurrentPage(page);
  };

  // Handle search
  const handleSearch = (term) => {
    setSearchTerm(term);
    setCurrentPage(1);
  };

  // Handle search field change
  const handleSearchFieldChange = (field) => {
    setSearchField(field);
    setCurrentPage(1);
  };

  // Handle sort
  const handleSort = (field, direction) => {
    setSortField(field);
    setSortDirection(direction);
  };

  // This function is not currently used but may be needed for future tab functionality

  // Handle view submission - navigate to the review page
  const handleViewSubmission = (submission) => {
    // Pass the status as a query parameter to the review page
    router.push(`/dashboard/submission-management/hec-diary/review/${submission.id}?status=${submission.status}`);
  };

  // Define table columns
  const columns = [
    { field: '#',  sortable: false },
    {
      field: 'studentName',
      label: 'USER NAME',
      sortable: false,
      cellRenderer: (value) => (
        <div className="flex items-center">
          <div className="w-8 h-8 rounded-full bg-gray-200 mr-3 overflow-hidden">
            {/* You can add a profile image here if available */}
            <div className="w-full h-full flex items-center justify-center text-gray-500">
              {value?.charAt(0)?.toUpperCase() || 'U'}
            </div>
          </div>
          <span>{value}</span>
        </div>
      )
    },
   {
  field: 'status',
  label: 'REVIEW STATUS',
  sortable: false,
  cellRenderer: (_, row) => {
    const isReviewed = row.status === 'reviewed' || row.reviewedByCurrentTutor || row.underReviewByOtherTutor;
    const isNew = row.status === 'new';
    const isSubmitted = row.status === 'submit';

    return (
      <div className="flex items-center">
        <span className={`px-3 py-1 rounded-full text-xs font-medium ${
          isReviewed
            ? 'bg-green-100 text-green-800'
            : isNew
              ? 'bg-yellow-100 text-yellow-800'
              : 'bg-red-100 text-red-800'
        }`}>
          {isReviewed ? 'Reviewed' : isNew ? 'Incomplete' : isSubmitted ? 'Submitted' : 'Not Reviewed Yet'}
        </span>
        {isSubmitted && (
          <span className="ml-2 text-red-500">✕</span>
        )}
        {isNew && (
          <span className="ml-2 text-yellow-500">⏱</span>
        )}
        {isReviewed && (
          <span className="ml-2 text-green-500">✓</span>
        )}
      </div>
    );
  }
},
    {
      field: 'title',
      label: 'MESSAGE',
      sortable: false,
      cellRenderer: (value) => (
        <div className="truncate max-w-xs">
          {value || "No message"}
        </div>
      )
    },
    {
      field: 'moduleLevel',
      label: 'STUDENT ATTEMPT',
      sortable: false,
      cellRenderer: (value) => (
        <div className="text-center">
          {value || 0}
        </div>
      )
    },
    
  ];

  // Define actions for table rows
const actions = [
    {
      icon: 'heroicons-outline:eye',
      className: (row) => row.status !== 'new'
        ? 'text-blue-600 hover:text-blue-700 cursor-pointer'
        : 'text-gray-400 cursor-not-allowed', // Added cursor-not-allowed here
      onClick: (row) => {
        handleViewSubmission(row); // Keep the simplified onClick
      },
      condition: (row) => row.status !== 'new', // Only enable if status is not 'new'
    }
  ];

  // Define search filter options
  const searchFilterOptions = [
    { label: 'Student Name', value: 'studentName' },
    { label: 'Title', value: 'title' }
  ];



  return (
    <div className="bg-white p-6 rounded-lg shadow">


      {/* Show loading or error states */}
      {error && (
        <div className="mb-4 p-4 bg-red-100 text-red-700 rounded-md">
          Error loading data: {error.message}
        </div>
      )}

      {/* Table */}
      <NewTablePage
        columns={columns}
        data={submissions}
        actions={actions}
        loading={loading}
        title='Todays Diary'
        // Pagination props
        currentPage={currentPage}
        totalPages={totalPages}
        changePage={handlePageChange}
        totalItems={totalItems}
        rowsPerPage={rowsPerPage}
        setRowsPerPage={setRowsPerPage}

        // Search and filter props
        showSearch={false}
        showNameFilter={false}
        showSortFilter={false}

        // Pass current state values
        searchTerm={searchTerm}
        searchField={searchField}
        sortField={sortField}
        sortDirection={sortDirection}

        // Pass handlers for search, filter and sort
        onSearch={handleSearch}
        onNameFilterChange={handleSearchFieldChange}
        onSort={handleSort}

        // Pass name filter options
        nameFilterOptions={searchFilterOptions}
      />
    </div>
  );
};

export default TodaysDiary;
