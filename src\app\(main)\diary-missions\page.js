'use client';
import DetailsModal from '@/components/form/modal/MissionConfirmationModal';
import GoBack from '@/components/shared/GoBack';
import useDataFetch from '@/hooks/useDataFetch';
import { Icon } from '@iconify/react';
import Image from 'next/image';
import React, { useState } from 'react';

const DiaryMission = () => {
  const { data, isLoading } = useDataFetch({
    queryKey: 'mission-diary',
    endPoint: '/diary/missions',
  });
  const missions = data?.items || [];

  const [selectedMission, setSelectedMission] = useState(null);

  return (
    <div>
      <Image
        height={300}
        width={500}
        src={'/assets/images/all-img/target.png'}
        className="absolute left-0 bottom-0 z-0 max-w-80 z-0"
        alt={'ask'}
      />
      <div className="max-w-7xl mx-auto p-5 xl:px-0 space-y-8 relative z-10">
        <GoBack title={'HEC Play'} linkClass="my-5 mb-8 w-full max-w-40" />
        <div className="flex items-center justify-between text-gray-600 bg-[#FFF9FB] p-5 min-h-60 shadow-lg rounded-lg relative border">
          <div>
            <h1 className="text-2xl text-yellow-800 font-semibold">
              Hello English Coaching Mission Diary
            </h1>
            {/* <p>Instruction:</p>
                <p>1. Write a short writtings on a dog.</p> */}
          </div>

          <h1 className="bg-gradient-to-b from-[#ECB306] to-[#AE6E33] bg-clip-text text-3xl font-extrabold text-transparent font-serif pr-20">
            Mission Diary
          </h1>

          <div className="absolute -top-6 -right-6">
            <Image
              src="/assets/images/all-img/footer_butterfly.png"
              alt="mission badge"
              width={100}
              height={100}
            />
          </div>

          <Image
            src="/assets/images/all-img/target2.png"
            alt="mission shape"
            width={120}
            height={120}
            className="absolute top-1/2 mt-4 -translate-y-1/2 right-6"
          />
        </div>

        <div className='bg-[#FCF8EF] p-5 rounded-lg shadow-lg text-center py-5'>
          <div className=" max-w-[377px] mx-auto space-y-8">
            <button className="bg-gradient-to-b from-[#FFDE5B] to-[#DCA600] min-w-60 py-2 text-2xl rounded-lg">
              Mission Diary
            </button>

            {missions?.map((item, idx) => {
              // Calculate progress percentage for this mission
              const taskProgress = item?.progress || 0;
              const progressPercent = `${taskProgress}%`;
              const isToday = item?.publishDate === new Date().toLocaleDateString();

              // Get the task title
              const taskTitle = item?.title || 'Mission Task';

              return (
                <div key={`${idx}`}>
                  <div className="relative w-full">
                    <div
                      className={`flex items-center gap-2 ${
                        idx % 2 == 0
                          ? 'justify-start'
                          : 'justify-start flex-row-reverse'
                      } w-full`}
                    >
                      <div
                        onClick={() => setSelectedMission(item)}
                        className={`h-12 w-12 rounded-full cursor-pointer ${
                          taskProgress === 100
                            ? 'bg-green-400 border-green-400'
                            : isToday ? 'bg-yellow-500 border-yellow-600' : 'bg-yellow-500 border-yellow-500'
                        } border-4 flex items-center justify-center relative`}
                      >
                        <div
                          className={`h-10 w-10 rounded-full ${
                            taskProgress === 100
                              ? 'bg-green-400'
                              : 'bg-yellow-500'
                          } border border-white flex items-center justify-center text-xs font-medium text-white`}
                        >
                          {progressPercent}
                        </div>
                      </div>

                      <div>
                        <p className="font-semibold">{taskTitle}</p>
                        {taskProgress === 100 && <p className="">Complete</p>}
                      </div>
                    </div>

                    {/* Only show connecting line if not the last item in the group */}
                    {idx < missions?.length - 1 && (
                      <Image
                        src={
                          idx % 2 == 0
                            ? '/assets/images/all-img/mission-shape2.png'
                            : '/assets/images/all-img/mission-shape1.png'
                        }
                        alt={'arrow'}
                        width={280}
                        height={20}
                        className={`absolute max-w-[70%] sm:w-full left-1/2 -translate-x-1/2 ${
                          idx % 2 == 0 ? '-bottom-5' : '-bottom-6'
                        }`}
                      />
                    )}
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </div>


      <DetailsModal 
        isOpen={!!selectedMission}
        onClose={() => setSelectedMission(null)}
        data={selectedMission?.description}
        title="Mission Diary"
        link={`/diary-missions/submission/${selectedMission?.id}`}
      />
    </div>
  );
};

export default DiaryMission;
