'use client';
import NewTablePage from "@/components/form/NewTablePage";
import Link from 'next/link';
import React, { useState } from 'react';
import { Formik } from 'formik';
import FormInput from '@/components/form/FormInput';
import { Form } from 'formik';
import * as Yup from 'yup';
import { useRouter } from 'next/navigation';



const createStage = () => {

  const validationSchema = Yup.object().shape({
      questions: Yup.string().required('Question is required'),
      points: Yup.string().required('Points is required'),
      minimum_word: Yup.string().required('Minimumm Word is required'),
    
    });


    const initialValues = {
      questions: '',
      points: '',
      minimum_word: '',
      
    };

    const handleSubmit = (values, {setSubmitting }) => {
      console.log(values);
      setSubmitting(false);    };
  
  

  return (
    <div className= 'min-h-screen bg-white p-5'> 

<h1 className="card-title text-black text-xl mb-3">Create Stage</h1>
    <Formik
    initialValues={initialValues}
    validationSchema={validationSchema}
    onSubmit={handleSubmit}
  >
    {({ isSubmitting, errors, touched }) => (
      <Form className="mx-auto bg-gray-100 p-6 rounded-lg shadow-md">
        <div className="flex flex-wrap gap-4 mb-4">

        <div className="flex-1 min-w-[200px]">
                <label htmlFor="id" className="block font-bold">
                  Default Stage 
                  {/* <span className="text-red-500">*</span> */}
                </label>
                <FormInput
                  type="text"
                  name="defaultstage"
                  id="defaultstage"
                  placeholder="0 Words"
                //   className={`w-full mt-1 p-2 border ${
                //     errors.id && touched.id ? 'border-red-500' : 'border-gray-300'
                //   } rounded`}
                />
                {/* {errors.id && touched.id && (
                  <div className="text-red-500 text-sm">{errors.id}</div>
                )} */}
              </div>

              

              
        </div>
      </Form>
    
    
    
    )}



     

    </Formik>

    <div className="flex justify-end gap-4 mt-7">
  <button
    type="submit"
    className="bg-gray-300 hover:bg-gray-50 text-black font-medium py-2 px-4 rounded"
  >
    Cancel
  </button>

  <button
    type="submit"
    className="bg-[#FFDE34] hover:bg-yellow-300 text-black font-medium py-2 px-4 rounded"
  >
    Save Changes
  </button>
</div>

      
    </div>
  );
};

export default createStage;
