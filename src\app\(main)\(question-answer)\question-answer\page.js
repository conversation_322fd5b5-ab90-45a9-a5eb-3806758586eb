'use client';
import Button, { ButtonIcon } from '@/components/Button';
import <PERSON><PERSON>ie<PERSON> from '@/components/EditorViewer';
import DetailsModal from '@/components/form/modal/MissionConfirmationModal';
import TinyMceEditor from '@/components/form/TinyMceEditor';
import GoBack from '@/components/shared/GoBack';
import useDataFetch from '@/hooks/useDataFetch';
import api from '@/lib/api';
import { Editor } from '@tinymce/tinymce-react';
import { Form, Formik } from 'formik';
import { useSearchParams } from 'next/navigation';
import React, { useEffect, useRef, useState } from 'react';
import { useSelector } from 'react-redux';

const HecAnswer = () => {
  const searchParams = useSearchParams().get('submission');
  const editorRef = useRef(null);
  const [value, setValue] = useState('');
  const { user } = useSelector((state) => state.auth);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [showDetailsModal, setShowDetailsModal] = useState(false);

  const { data, refetch } = useDataFetch({
    queryKey: ['hec-answer', searchParams],
    endPoint: searchParams ? `/student-qa-mission/${searchParams}` : `/qa/latest-assignments/${user?.id}`,
  });

  const showSubmission = data?.submission?.answer && !isSubmitted;
  const showFeedback = data?.submission?.answer && data?.submission?.feedback;

  // Count words in HTML content
  const countWords = (html) => {
    if (!html) return 0;
    // Remove HTML tags
    const text = html.replace(/<[^>]*>/g, ' ');
    // Remove entities
    const cleanText = text.replace(/&nbsp;|&amp;|&lt;|&gt;|&quot;|&#39;/g, ' ');
    // Remove extra spaces and split by whitespace
    const words = cleanText
      .trim()
      .split(/\s+/)
      .filter((word) => word.length > 0);
    return words.length;
  };

  const handleSubmit = async (values) => {
    try {
      // Check minimum word count
      const wordCount = countWords(values.answer);
      const minimumWords = data?.question?.minimumWords || 0;

      if (wordCount < minimumWords) {
        alert(
          `Your answer must contain at least ${minimumWords} words. Current word count: ${wordCount}`
        );
        return;
      }

      const response = await api.put(
        `/qa/submissions/${data?.submission?.id}/submit`,
        values
      );
      console.log(response);
      setIsSubmitted(true);
    } catch (error) {
      console.log(error);
    }
  };

  const handleUpdate = async (values) => {
    try {
      // Ensure we have the answer value
      if (!values || !values.answer) return;

      // For explicit submissions (not auto-save), check minimum word count
      // if (!values._autoSave) {
      //   const wordCount = countWords(values.answer);
      //   const minimumWords = data?.question?.minimumWords || 0;

      //   if (wordCount > minimumWords) {
      //     alert(`Your answer must not exceed ${minimumWords} words. Current word count: ${wordCount}`);
      //     return;
      //   }
      // }

      const response = await api.post(
        `/qa/submissions`,
        {
          answer: values.answer,
          assignmentId: data?.id,
        },
        { showSuccessToast: false }
      );

      console.log('Auto-saved:', response);

      // Only refetch and update UI state on explicit submission, not auto-save
      if (!values._autoSave) {
        refetch();
        setIsSubmitted(false);
      }
    } catch (error) {
      console.error('Error updating submission:', error);
    }
  };

  // Handle component unmount - save draft
  useEffect(() => {
    return () => {
      // Save draft when navigating away if there's content
      if (value && !isSubmitted && data?.id) {
        handleUpdate({
          answer: value,
          _autoSave: true,
        });
      }
    };
  }, [value, isSubmitted, data?.id]);

  return (
    <div className="relative">
      <div className="max-w-7xl mx-auto px-5 xl:px-0 relative z-10">
        <GoBack title={'HEC Play'} linkClass="my-5 mb-8 w-full max-w-40" />

        <div className="p-5 rounded-lg bg-[#FFF9FB] shadow-lg space-y-5 mb-10">
          <div className="p-5 bg-[#FCF8EF] rounded-lg [box-shadow:2px_2px_12px_0px_#F5D1B066_inset,_-2px_-2px_12px_0px_#F5D1B066_inset]">
            <h1 className="text-2xl text-yellow-800 font-semibold">
              {data?.question?.question}
            </h1>
            <p>Instruction:</p>
            <EditorViewer data={data?.instructions} />
          </div>

          {showSubmission ? (
            <div className="space-y-3">
              <div className="p-5 shadow-lg rounded-lg border relative bg-white">
                <h1 className="text-xl text-yellow-800 font-semibold">
                  My Submission
                </h1>
                <EditorViewer
                  data={
                    data?.submission?.answer?.length > 200
                      ? data?.submission?.answer?.slice(0, 400) + '...'
                      : data?.submission?.answer
                  }
                />

                <div className="absolute right-2 top-2">
                  <ButtonIcon
                    icon={'ri:edit-2-fill'}
                    innerBtnCls={'h-10 w-10'}
                    btnIconCls={'h-5 w-5'}
                    onClick={() => setIsSubmitted(true)}
                  />
                </div>
              </div>

              <div className="p-5 shadow-lg rounded-lg border relative bg-white">
                <h1 className="text-xl text-yellow-800 font-semibold">
                  Tutor Correction Zone
                </h1>
                {showFeedback && (
                  <>
                    {data?.submission?.corrections?.grammar?.length > 0 && (
                      <div className="rounded-md">
                        <ul className=" text-sm text-gray-800">
                          {data.submission.corrections.grammar.map(
                            (item, index) => (
                              <EditorViewer key={index} data={item} />
                            )
                          )}
                        </ul>
                      </div>
                    )}

                    <div className="absolute right-2 top-2">
                      <ButtonIcon
                        icon={'arcticons:feedback-2'}
                        innerBtnCls={'h-10 w-10'}
                        btnIconCls={'h-4 w-4'}
                        onClick={() => setShowDetailsModal(true)}
                      />
                    </div>
                  </>
                )}

                <p
                  className={`${
                    !(data?.submission?.status === 'reviewed') && 'text-red-600'
                  } text-center mt-2`}
                >
                  {!(data?.submission?.status === 'reviewed') &&
                    'Not Confirmed yet'}
                </p>
              </div>
            </div>
          ) : (
            <Formik
              initialValues={{
                answer: value || data?.submission?.answer || '',
              }}
              onSubmit={data?.submission?.answer ? handleUpdate : handleSubmit}
              enableReinitialize
            >
              {() => (
                <Form>
                  <TinyMceEditor
                    name="answer"
                    editorRef={editorRef}
                    initialValue={data?.submission?.answer || value}
                    onAutoSave={(content) =>
                      handleUpdate({ ...content, _autoSave: true })
                    }
                    setValue={setValue}
                    maxWords={
                      data?.question?.minimumWords
                        ? data?.question?.minimumWords
                        : 500
                    }
                  />

                  <div className="flex justify-center mt-3 gap-3">
                    <Button
                      buttonText="Cancel"
                      type="button"
                      onClick={() => setIsSubmitted(false)}
                    />
                    <Button
                      buttonText={
                        data?.submission?.answer ? 'Update' : 'Submit'
                      }
                      type="submit"
                      className="bg-yellow-400 hover:bg-yellow-500 text-black"
                    />
                  </div>
                </Form>
              )}
            </Formik>
          )}
        </div>
      </div>

      <DetailsModal
        isOpen={showDetailsModal}
        onClose={() => setShowDetailsModal(false)}
        data={data?.submission?.corrections?.grammar}
        title="Teachers Feedback"
        link={`/answer`}
        showBtn={false}
      />
    </div>
  );
};

export default HecAnswer;
