'use client';
import React, { useState } from 'react';
import NewTablePage from "@/components/form/NewTablePage";

// DiaryGoal component that uses tab switching instead of routing
const DiaryGoal = ({ onChangeTab }) => {
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [currentPage, setCurrentPage] = useState(1);
  
  // Dummy data for questions
  const dummyQuestions = [
    {
      id: '1',
      goalStage: '1',
      wordCount: 5,
    },
    {
      id: '2',
      goalStage: '1',
      wordCount: 5,
    },
  ];

  // Pagination logic
  const totalItems = dummyQuestions.length;
  const startIndex = (currentPage - 1) * rowsPerPage;
  const endIndex = startIndex + rowsPerPage;
  const paginatedQuestions = dummyQuestions.slice(startIndex, endIndex);

  // Define columns for the table
  const columns = [
    {
      label: 'Goal Stage',
      field: 'goalStage',
    },
    {
      label: 'Word Count',
      field: 'wordCount',
    },
  ];

  // Handle page change
  const handleChangePage = (page) => {
    setCurrentPage(page);
  };

  // Use tab switching instead of routing
  const handleAddStage = () => {
    // Switch to the createStage tab
    if (typeof onChangeTab === 'function') {
      onChangeTab('createStage');
    } else {
      console.warn('onChangeTab prop not provided to DiaryGoal component');
    }
  };
  
  return (
    <div className="container mx-auto p-4">
      <NewTablePage
        title="Today's Diary Goal"
        createButton="Add Stage"
        columns={columns}
        data={paginatedQuestions}
        currentPage={currentPage}
        changePage={handleChangePage}
        totalItems={totalItems}
        rowsPerPage={rowsPerPage}
        setRowsPerPage={setRowsPerPage}
        totalPages={Math.ceil(totalItems / rowsPerPage)}
        openCreateModal={handleAddStage}
        showCheckboxes={false}
        showSearch={false}
        showNameFilter={false}
        showSortFilter={false}
      />
    </div>
  );
};

export default DiaryGoal;