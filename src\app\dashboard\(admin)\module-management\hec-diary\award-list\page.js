'use client';

import NewTablePage from '@/components/form/NewTablePage';
import useDataFetch from '@/hooks/useDataFetch';
import { useRouter } from 'next/navigation';
import React, { useState } from 'react';

const MissionQAList = () => {
  const [activeTab, setActiveTab] = useState('monthly');
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [currentPage, setCurrentPage] = useState(1);
  const router = useRouter();

  // monthly and yearly filtering is missing in the API 

  const { data, isLoading, refetch } = useDataFetch({
    queryKey: ['awards', currentPage, rowsPerPage, activeTab],
    endPoint: `/awards/admin${activeTab === 'monthly' ? '?frequency=monthly' : '?frequency=yearly'}`,
    params: { page: currentPage, limit: rowsPerPage },
  });

  const awards = data?.items || [];
  const totalItems = awards.length;

  // Pagination logic
  const startIndex = (currentPage - 1) * rowsPerPage;
  const endIndex = startIndex + rowsPerPage;
  const paginatedItems = awards.slice(startIndex, endIndex);

  const handleTabChange = (tab) => {
    setActiveTab(tab);
    setCurrentPage(1);
  };

  const handleEdit = (row) => {
    console.log('Edit', row);
    // Add your edit logic here
  };

  const handleDelete = (row) => {
    console.log('Delete', row);
    // Add your delete logic here
  };

  // Define actions array for row operations
  const actions = [
    // {
    //   name: 'view',
    //   icon: 'material-symbols:visibility',
    //   className: 'text-blue-600',
    //   onClick: (row) => router.push(`/dashboard/module-management/hec-diary/award-list/${row?.id}`),
    //   // onClick: handleView,
    // },
  ];

  const columns = [
    {
      label: 'AWARD TITLE',
      field: 'name',
    },
  ];

  const handleChangePage = (page) => {
    setCurrentPage(page);
  };

  return (
    <div className="w-full px-4">
      <div className="mb-4">
        <div className="flex mb-6 bg-gradient-to-b from-transparent to-gray-100 border-b h-16">
          <button
            className={`px-6 py-2 border-b-2 text-lg font-medium mr-2 ${
              activeTab === 'monthly'
                ? 'border-yellow-300 text-black bg-[#FEFCE8]'
                : 'border-transparent text-gray-700'
            }`}
            onClick={() => handleTabChange('monthly')}
          >
            Monthly Awards
          </button>
          <button
            className={`px-6 py-2 border-b-2 text-lg font-medium ${
              activeTab === 'annual_award'
                ? 'border-yellow-300 text-black bg-[#FEFCE8]'
                : 'border-transparent text-gray-700'
            }`}
            onClick={() => handleTabChange('annual_award')}
          >
            Annual Awards
          </button>
        </div>
      </div>

      <NewTablePage
        showSearch={false}
        showNameFilter={false}
        title=''
        showSortFilter={false}
        showCreateButton={false}
        data={paginatedItems}
        columns={columns}
        actions={actions}
        isLoading={false}
        currentPage={currentPage}
        rowsPerPage={rowsPerPage}
        totalItems={totalItems}
        onChangePage={handleChangePage}
        onChangeRowsPerPage={setRowsPerPage}
        totalPages={Math.ceil(totalItems / rowsPerPage)}
        showCheckboxes={false}
      />
    </div>
  );
};

export default MissionQAList;
