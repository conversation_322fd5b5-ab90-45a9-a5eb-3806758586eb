'use client';

import React, { useState, useEffect } from 'react';
import { useParams, useRouter, useSearchParams } from 'next/navigation';
import { toast } from 'sonner';
import api from '@/lib/api';
import useDataFetch from '@/hooks/useDataFetch';
import DiaryCanvas from '../../_components/DiaryCanvas';
import HecDiaryLayout from '../../_components/HecDiaryLayout';
import FeedbackModal from '../../_components/FeedbackModal';
import { Icon } from '@iconify/react';
import { formatDate } from '@/utils/dateFormatter';

const MissionDiaryReviewPage = () => {
  const { id } = useParams();
  const router = useRouter();
  const [correctionText, setCorrection] = useState('');
  const [score, setScore] = useState('');
  const [isFeedbackModalOpen, setIsFeedbackModalOpen] = useState(false);
  const searchParams = useSearchParams();
  const tabParam = searchParams.get('tab');
  const [activeTab, setActiveTab] = useState(tabParam || 'missionDiary');
  
  useEffect(() => {
    if (tabParam) {
      setActiveTab(tabParam);
    }
  }, [tabParam]);

  // Fetch mission diary entry data
  const { data, isLoading, error } = useDataFetch({
    queryKey: 'mission-diary-entry-review',
    endPoint: `/diary/tutor/missions/entries/${id}`,
    method: 'get',
    params: {},
    enabled: !!id,
  });

  // Initialize correction text with original content when data is loaded
  useEffect(() => {
    if (data?.content) {
      setCorrection(data.content);
    }
  }, [data?.content]);

  // Submit review function
  const submitReview = async () => {
    if (!score) {
      toast.error('Please provide a score');
      return;
    }

    try {
      const response = await api.post(`/tutor/diary/mission/entries/${id}/correction`, {
        correctionText,
        score: parseInt(score),
      });

      if (response.success) {
        toast.success('Review submitted successfully');
        router.push('/dashboard/submission-management/hec-diary?tab=missionDiary');
      } else {
        throw new Error(response.message);
      }
    } catch (err) {
      toast.error(err.message || 'Failed to submit review');
    }
  };

  const handleBack = () => {
    router.push('/dashboard/submission-management/hec-diary?tab=missionDiary');
  };

  if (isLoading) {
    return (
      <HecDiaryLayout activeTab={activeTab}>
        <div className="flex items-center justify-center h-96">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-yellow-500"></div>
        </div>
      </HecDiaryLayout>
    );
  }

  if (error) {
    return (
      <HecDiaryLayout activeTab={activeTab}>
        <div className="bg-red-100 text-red-700 p-4 rounded-md">
          Error loading diary entry: {error.message}
        </div>
      </HecDiaryLayout>
    );
  }

  return (
    <HecDiaryLayout activeTab={activeTab}>
        <div className="flex justify-between items-center mb-2">
          <div className="flex gap-4 items-center">
            <h6 className="text-sm text-black font-medium ">
              Submitted by:
            </h6>
            <h2 className="text-[#464646] font-normal">
              {data?.diary?.userName}
            </h2>
          </div>
          <div className="flex items-center gap-3 text-sm text-gray-500">
            <div className="font-medium text-sm text-black ">Date:</div>
            <div className="flex items-center gap-3">
              <Icon
                icon="uil:calender"
                width="24"
                height="24"
                className="mx-auto text-gray-900 p-1 rounded-full bg-[#FFF189]"
              />
              {formatDate(data.entryDate, 'ordinal')}
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 items-center bg-[#FDE7E9] gap-2 p-1 shadow-xl">
          {/* Diary Canvas */}
          <div className="bg-white h-full flex items-center justify-center p-2 overflow-hidden shadow-xl">
            <div className="w-full h-[500px] flex items-center justify-center overflow-hidden">
              <div
                className="canvas-container-wrapper"
                style={{ width: '100%', height: '100%', padding: '20px' }}
              >
                <DiaryCanvas data={data} />
              </div>
            </div>
          </div>
          
          {/* Review Section */}
          <div className="p-2 shadow-xl h-full bg-white">
            {/* Original Content */}
            <div className="mb-4 rounded-md shadow-lg p-4 h-[204px] overflow-y-auto">
              <div className="flex justify-between items-center border-b-2 border-dashed border-gray-300 mb-3">
                <h3 className="text-lg font-semibold mb-2">Original Content</h3>
                <div className="flex items-center gap-3 text-sm">
                  Date: {formatDate(data.entryDate, 'ordinal')}
                </div>
              </div>
              <p className="whitespace-pre-wrap text-sm text-[#314158]">
                {data.content}
              </p>
            </div>
            
            {/* Correction Section */}
            <div className="mb-4 rounded-md shadow-lg p-4 h-[204px] overflow-y-auto">
              <div className="flex justify-between items-center border-b-2 border-dashed border-gray-300 mb-3">
                <h3 className="text-lg font-semibold mb-2">Correction</h3>
                <div className="flex items-center gap-3 text-sm">
                  <span className="text-gray-500">
                    {correctionText.length} characters
                  </span>
                </div>
              </div>
              <textarea
                value={correctionText}
                onChange={(e) => setCorrection(e.target.value)}
                className="w-full h-[120px] p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-yellow-500 text-sm"
              />
              
              <div className="flex items-center justify-between">
                <div className='flex items-center gap-5'>
                  <button
                    onClick={() => setIsFeedbackModalOpen(true)}
                    className="px-4 py-1 bg-[#FEFCE8] text-base text-[#723F11] rounded-md border border-[#723F11] font-medium hover:bg-[#FFF8D6]"
                  >
                    Give feedback
                  </button>
                  <div className="flex items-center">
                    <label className="mr-2">Score:</label>
                    <input
                      type="number"
                      value={score}
                      onChange={(e) => setScore(e.target.value)}
                      className="w-16 border border-gray-300 rounded px-2 py-1"
                      min="0"
                      max="100"
                      placeholder="0-100"
                    />
                  </div>
                </div>
                <button
                  onClick={submitReview}
                  disabled={!correctionText.trim() || !score}
                  className="px-4 py-2 bg-yellow-500 text-white rounded-md hover:bg-yellow-600 disabled:bg-gray-300 disabled:cursor-not-allowed"
                >
                  Submit Review
                </button>
              </div>
            </div>
          </div>
        </div>

      <FeedbackModal
        isOpen={isFeedbackModalOpen}
        onClose={() => setIsFeedbackModalOpen(false)}
        entryId={id}
      />
    </HecDiaryLayout>
  );
};

export default MissionDiaryReviewPage;
